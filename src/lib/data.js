export async function fetchAbletonData() {
  try {
    const response = await fetch('/ableton_data_export.json');
    if (!response.ok) throw new Error('Failed to fetch JSON');
    return await response.json();
  } catch (error) {
    console.error('Error fetching Ableton data:', error);
    return null;
  }
}

export function getAudioFilePath(data) {
  // Assume first audio clip's file_path or default to 'audio.wav'
  const audioClip = data.clips.find(clip => clip.is_audio);
  return audioClip?.file_path || '/audio.wav';
}

export function getTransientsForClip(data, clipName) {
  return data.transient_markers.filter(t => t.clip_name === clipName);
}